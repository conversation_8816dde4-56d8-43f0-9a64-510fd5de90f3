"use client";

import { useEvent, useMap } from "@geon-map/react-odf";
import { cn } from "@geon-ui/react/lib/utils";
import { Label } from "@geon-ui/react/primitives/label";
import { Switch } from "@geon-ui/react/primitives/switch";
import { useEffect, useState } from "react";

interface MouseCoordWidgetProps {
  className?: string;
}

export const MouseCoordWidget = ({ className }: MouseCoordWidgetProps) => {
  const [enabled, setEnabled] = useState(false);
  const [coordinate, setCoordinate] = useState<{ x: number; y: number } | null>(
    null,
  );
  const { registerListener } = useEvent();
  const { map, isReady } = useMap();

  if (!isReady || !map) {
    throw new Error(
      "Map instance has not been initialized. Make sure you're using this hook inside a Map component.",
    );
  }

  useEffect(() => {
    if (!enabled || !map) {
      setCoordinate(null);
      return;
    }

    // 마우스 좌표 추적 이벤트 등록
    const cleanup = registerListener(map, "pointermove", (event: any) => {
      try {
        const coord = map.getCoordinateFromPixel(event.pixel);
        if (coord) {
          setCoordinate({ x: coord[0], y: coord[1] });
        }
      } catch (e) {
        console.warn("좌표 업데이트 실패:", e);
      }
    });

    return cleanup;
  }, [enabled, map, registerListener]);

  return (
    <div
      className={cn(
        "absolute bottom-4 left-1/2 -translate-x-1/2 z-50 w-fit rounded-lg border p-4 shadow-sm bg-white text-sm",
        className,
      )}
    >
      <div className="flex flex-col gap-3">
        <div className="flex items-center gap-3">
          <Label htmlFor="coord-switch" className="text-sm font-medium">
            좌표 표시
          </Label>
          <Switch
            id="coord-switch"
            checked={enabled}
            onCheckedChange={setEnabled}
          />
        </div>

        {enabled && (
          <div className="grid grid-cols-2 gap-2 text-gray-700">
            <div className="flex flex-col items-center">
              <span className="font-bold">X</span>
              <span className="min-w-[140px] text-center tabular-nums">
                {coordinate?.x?.toFixed(8) ?? "-"}
              </span>
            </div>
            <div className="flex flex-col items-center">
              <span className="font-bold">Y</span>
              <span className="min-w-[140px] text-center tabular-nums">
                {coordinate?.y?.toFixed(8) ?? "-"}
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
