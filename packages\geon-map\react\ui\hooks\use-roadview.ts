"use client";
// use-roadview.ts
import { <PERSON><PERSON>, MarkerEventHandlers } from "@geon-map/core";
import {
  useDraw,
  useFeatureActions,
  useLayer,
  useMap,
} from "@geon-map/react-odf";
import { MAP_PROXY } from "@geon-query/model";
import { useState } from "react";

import MapMarkerIcon from "../resource/images/map-marker.png";

interface UseRoadviewProps {
  onMarkerSelect: (marker: any) => void;
  onCenterSelect?: (center: [number, number]) => void;
}

export function useRoadview({
  onMarkerSelect,
  onCenterSelect,
}: UseRoadviewProps) {
  const [enabled, setEnabled] = useState(false);
  const [resizableWidth, setResizableWidth] = useState(160);
  const [prevWidth, setPrevWidth] = useState(420);
  const [roadviewLayer, setRoadviewLayer] = useState<any | null>(null);
  const { startDrawing } = useDraw();
  const { map, odf, isReady } = useMap();

  if (!isReady || !map || !odf) {
    throw new Error(
      "Map instance has not been initialized. Make sure you're using this hook inside a Map component.",
    );
  }
  const { drawLayer } = useLayer();
  const { deleteFeature } = useFeatureActions();

  const roadviewLayerOption = {
    service: "xyz",
    projection: "EPSG:5181",
    extent: [-30000, -60000, 494288, 988576],
    tileGrid: {
      origin: [-30000, -60000],
      resolutions: [
        4096, 2048, 1024, 512, 256, 128, 64, 32, 16, 8, 4, 2, 1, 0.5, 0.25,
        0.125,
      ],
      matrixIds: [
        "0",
        "1",
        "2",
        "3",
        "4",
        "5",
        "6",
        "7",
        "8",
        "9",
        "10",
        "11",
        "12",
        "13",
        "14",
        "15",
      ],
    },
    server: {
      proxyURL: `${MAP_PROXY}`,
      proxyParam: "url",
      url: `https://map.daumcdn.net/map_k3f_prod/bakery/image_map_png/PNGSD_RV01/v16_o9kb2/{{15-z}}/{{-y-1}}/{{x}}.png`,
    },
  };

  const enableRoadView = () => {
    setEnabled(true);
    setResizableWidth(prevWidth);
  };

  const disableRoadView = () => {
    setEnabled(false);
    setPrevWidth(resizableWidth);
    setResizableWidth(160);
    map.removeLayer(roadviewLayer);
  };

  const roadviewLayerOn = () => {
    const layer = odf.LayerFactory.produce("api", roadviewLayerOption);
    layer.setOpacity(0.5);
    layer.setMap(map);
    setRoadviewLayer(layer);
  };

  // (중복 정의 제거됨)

  const handlers: MarkerEventHandlers = {
    onDragEnd: (event) => {
      const newPosition: { _x: number; _y: number } = event.getPosition();
      onCenterSelect?.([newPosition._x, newPosition._y]);
      // 지도 중심 이동은 상위에서 처리하거나 옵션화 가능
    },
  };

  const handleToggle = (checked: boolean, marker: any) => {
    if (checked) {
      roadviewLayerOn();
      alert("지도에 지점을 선택해주세요");

      // startDrawing이 drawend 콜백을 반환하는 구조 활용
      const { drawend } = startDrawing("point");

      drawend(async (feature: any) => {
        try {
          const centerPoint = feature?.getCenterPoint?.() ||
            feature?.getGeometry?.()?.getCoordinates?.() || [0, 0];
          onCenterSelect?.(centerPoint as [number, number]);

          const markerObj = Marker.createAndAddMarker(
            map,
            {
              position: new odf.Coordinate(centerPoint),
              draggable: true,
              style: { src: MapMarkerIcon.src, height: "50px", width: "50px" },
            },
            handlers,
          ).odfMarker;

          onMarkerSelect(markerObj);
          enableRoadView();
        } catch (e) {
          console.error("포인트 선택 중 오류:", e);
        } finally {
          // ✅ drawend 완료 후 그리기 feature 삭제 - useFeatureActions 사용
          if (feature) {
            setTimeout(() => {
              console.log("🔍 [use-roadview] 그리기 feature 삭제 시도", {
                featureId: feature.getId?.(),
                featureType: feature.getGeometry?.()?.getType?.(),
                hasDrawLayer: !!drawLayer,
                drawLayerId: drawLayer?.id,
                component: "use-roadview",
              });

              const success = deleteFeature(feature, "draw");
              if (success) {
                console.log(
                  "✅ [use-roadview] useFeatureActions로 그리기 feature 삭제 완료",
                );
              } else {
                console.warn(
                  "⚠️ [use-roadview] useFeatureActions로 그리기 feature 삭제 실패",
                );
              }
            }, 0);
          }
        }
      });
    } else {
      Marker.removeMarker(marker);
      onMarkerSelect(null);
      disableRoadView();
    }
  };

  return {
    enabled,
    resizableWidth,
    setResizableWidth,
    prevWidth,
    setPrevWidth,
    handleToggle,
  };
}
