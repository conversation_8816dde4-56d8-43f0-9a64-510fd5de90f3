import React, { useContext, useEffect } from "react";

import {
  MapStoreContext,
  type MapStoreInitialOptions,
  MapStoreProvider,
} from "../contexts/map-store-context";
import { useMap } from "../hooks/use-map";
import { useMapActions } from "../hooks/use-map-actions";
import { cn } from "../lib/utils";
import type { MapInitializeOptions, UseMapReturn } from "../types/map-types";

interface SplitMode {
  count: 1 | 2 | 3 | 4;
}

interface MapInital extends Partial<MapInitializeOptions> {
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode;
  containerRef?: React.RefObject<HTMLDivElement>;
  autoInit?: boolean;
  onMapInit?: (mapState: UseMapReturn) => void;

  mapId?: string;
  initialStoreOptions?: MapStoreInitialOptions;

  splitMode?: SplitMode;
}

/**
 * Map 컴포넌트 (Components 레이어)
 * useMap, useMapActions 훅을 사용하여 ODF 지도를 초기화하고 상태를 관리합니다.
 * hook 또는 해당 컴포넌트를 사용하여 지도를 렌더링할 수 있습니다.
 *
 * @example
 * ```tsx
 * // 단일 지도
 * <Map className="w-full h-96" center={[127, 37]}>
 *   <DrawProvider />
 * </Map>
 * ```
 */

// 분할 지도 위치 정보
const POSITION_LABELS = {
  "top-left": "좌측 상단",
  "top-right": "우측 상단",
  "bottom-left": "좌측 하단",
  "bottom-right": "우측 하단",
} as const;

const POSITION_COLORS = {
  "top-left": "bg-green-600",
  "top-right": "bg-red-600",
  "bottom-left": "bg-blue-600",
  "bottom-right": "bg-purple-600",
} as const;

type Position = keyof typeof POSITION_LABELS;

// 분할 그리드 설정 함수
const getGridConfig = (count: number) => {
  switch (count) {
    case 1:
      return {
        gridClass: "grid-cols-1 grid-rows-1",
        positions: ["top-left"] as Position[],
      };
    case 2:
      return {
        gridClass: "grid-cols-2 grid-rows-1",
        positions: ["top-left", "top-right"] as Position[],
      };
    case 3:
      return {
        gridClass: "grid-cols-2 grid-rows-2",
        positions: ["top-left", "top-right", "bottom-left"] as Position[],
      };
    case 4:
      return {
        gridClass: "grid-cols-2 grid-rows-2",
        positions: [
          "top-left",
          "top-right",
          "bottom-left",
          "bottom-right",
        ] as Position[],
      };
    default:
      return {
        gridClass: "grid-cols-1 grid-rows-1",
        positions: ["top-left"] as Position[],
      };
  }
};

// 개별 지도 뷰 컴포넌트 (분할 지도용)
interface SingleMapViewProps extends Partial<MapInitializeOptions> {
  position: Position;
  onMapInit?: (mapState: UseMapReturn) => void;
  children?: React.ReactNode;
}

const SingleMapView = ({
  position,
  onMapInit,
  children,
  center,
  zoom,
  projection,
  ...mapInitializeOptions
}: SingleMapViewProps) => {
  const id = React.useId();
  const containerRef = React.useRef<HTMLDivElement>(null);
  const initializedRef = React.useRef(false);

  const { isReady, isLoading, error } = useMap({
    containerRef,
    center,
    zoom,
    projection,
    ...mapInitializeOptions,
    onMapInit,
  });

  const { setCenter, setZoom } = useMapActions();

  useEffect(() => {
    if (isReady && !initializedRef.current) {
      initializedRef.current = true;
      if (center && setCenter) {
        setCenter(center);
      }
      if (zoom !== undefined && setZoom) {
        setZoom(zoom);
      }
    }
  }, [isReady, center, zoom, setCenter, setZoom]);

  if (error) {
    return (
      <div className="relative h-full w-full flex items-center justify-center">
        <div className="text-red-500">지도 로딩 실패: {error}</div>
      </div>
    );
  }

  return (
    <div className="relative h-full w-full">
      <div id={`map-${id}`} ref={containerRef} className="w-full h-full" />
      {isReady && (
        <>
          {children}
          <div
            className={`absolute bottom-4 right-4 rounded px-3 py-1 text-sm text-white ${POSITION_COLORS[position]}`}
          >
            {POSITION_LABELS[position]}
          </div>
        </>
      )}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-75">
          <div className="text-gray-600">지도 로딩 중...</div>
        </div>
      )}
    </div>
  );
};

const MapContainer = ({
  className,
  style,
  children,
  onMapInit,
  center,
  zoom,
  projection,
  splitMode,
  ...mapInitializeOptions
}: Omit<
  MapInital,
  "mapId" | "enableMultiInstance" | "initialStoreOptions"
>) => {
  const id = React.useId();
  const containerRef = React.useRef<HTMLDivElement>(null);

  // 초기화 완료 추적을 위한 ref (무한루프 방지)
  const initializedRef = React.useRef(false);

  // 통합 지도 훅을 통한 초기화 및 상태 관리
  const { isReady, isLoading, error } = useMap({
    containerRef,
    center,
    zoom,
    projection,
    ...mapInitializeOptions,
    onMapInit,
  });

  // 지도 조작 액션들
  const { setCenter, setZoom } = useMapActions();

  // 🎯 성능 최적화: 초기화 시에만 center/zoom 설정 (무한루프 방지)
  useEffect(() => {
    if (isReady && !initializedRef.current) {
      initializedRef.current = true;

      // 초기 center 설정 (props로 전달된 경우에만)
      if (center && setCenter) {
        setCenter(center);
      }

      // 초기 zoom 설정 (props로 전달된 경우에만)
      if (zoom !== undefined && setZoom) {
        setZoom(zoom);
      }
    }
  }, [isReady, center, zoom, setCenter, setZoom]);

  // 에러 상태 표시
  if (error) {
    return (
      <div
        className={cn("relative flex items-center justify-center", className)}
        style={style}
      >
        <div className="text-red-500">지도 로딩 실패: {error}</div>
      </div>
    );
  }

  // 분할 모드 렌더링
  if (splitMode && splitMode.count > 1) {
    const { gridClass, positions } = getGridConfig(splitMode.count);

    return (
      <div className={cn("relative", className)} style={style}>
        <div className={`grid ${gridClass} gap-1 h-full w-full`}>
          {positions.slice(0, splitMode.count).map((position, index) => (
            <div
              key={`split-${index}`}
              className="relative border border-gray-300"
            >
              <MapStoreProvider mapId={`${id}-split-${index}`}>
                <SingleMapView
                  center={center}
                  zoom={zoom}
                  projection={projection}
                  position={position}
                  onMapInit={onMapInit}
                  {...mapInitializeOptions}
                >
                  {children}
                </SingleMapView>
              </MapStoreProvider>
            </div>
          ))}

          {/* 3분할일 때 빈 공간 */}
          {splitMode.count === 3 && (
            <div className="border border-gray-300 bg-gray-50 flex items-center justify-center text-gray-400">
              비어있음
            </div>
          )}
        </div>
      </div>
    );
  }

  // 단일 지도 모드 (기존 로직)
  return (
    <div className={cn("relative", className)} style={style}>
      <div id={`map-${id}`} ref={containerRef} className="w-full h-full" />
      {/* 지도가 완전히 준비된 후에만 children 렌더링 */}
      {isReady && children}
      {/* 로딩 상태 표시 (선택적) */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-75">
          <div className="text-gray-600">지도 로딩 중...</div>
        </div>
      )}
    </div>
  );
};

/**
 * 모든 Map 컴포넌트는 독립된 MapStoreProvider로 감싸집니다.
 */
export const Map = ({
  mapId,
  initialStoreOptions,
  splitMode,
  ...mapProps
}: MapInital) => {
  // 🎯 Context 존재 여부를 안전하게 확인 (에러 없이)
  const existingContext = useContext(MapStoreContext);

  // 🎯 Context가 없는 경우 항상 독립적인 Provider로 감싸기
  if (!existingContext) {
    return (
      <MapStoreProvider mapId={mapId} initialOptions={initialStoreOptions}>
        <MapContainer {...mapProps} splitMode={splitMode} />
      </MapStoreProvider>
    );
  }

  // 이미 Context가 있는 경우 (중첩된 Map 컴포넌트)
  return <MapContainer {...mapProps} splitMode={splitMode} />;
};
