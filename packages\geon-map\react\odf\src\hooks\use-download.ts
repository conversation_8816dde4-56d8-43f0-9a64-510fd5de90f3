import { Download, DownloadControlOptions } from "@geon-map/core";
import { useCallback, useEffect, useRef } from "react";

import { useMap } from "./use-map";

export interface UseDownloadOptions extends DownloadControlOptions {}
export interface UseDownloadReturn {
  downloadImage: () => void;
  downloadPDF: () => void;
}

export const useDownload = (
  options: UseDownloadOptions = {},
): UseDownloadReturn => {
  const { map, odf, isReady } = useMap();

  if (!isReady || !map || !odf) {
    throw new Error(
      "Map instance has not been initialized. Make sure you're using this hook inside a Map component.",
    );
  }

  const downloadRef = useRef<Download | null>(null);

  useEffect(() => {
    const download = new Download(odf, options);
    download.setMap(map);
    downloadRef.current = download;

    return () => {
      download.removeMap();
      downloadRef.current = null;
    };
  }, [map, odf, options]); // options를 의존성 배열에 다시 추가

  const downloadImage = useCallback(() => {
    downloadRef.current?.downloadImage();
  }, []);

  const downloadPDF = useCallback(() => {
    downloadRef.current?.downloadPDF();
  }, []);

  return { downloadImage, downloadPDF };
};
