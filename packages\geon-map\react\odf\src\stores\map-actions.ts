import { MapFactory } from "@geon-map/core";

import type { MapStoreContextType } from "../contexts/map-store-context";
import type { MapInitializeOptions } from "../types/map-types";
import { initializeBasicControlsForInstance } from "./controls-initializer";
import { CoreInstanceManager } from "./core-instances";
declare global {
  interface Window {
    odfMap: any;
  }
}
/**
 * 지도 초기화 액션
 * Core 패키지를 통해 지도를 생성하고 store 상태를 업데이트합니다.
 */
export async function initializeMap(
  container: HTMLElement,
  options: MapInitializeOptions,
  mapInstanceContext: MapStoreContextType,
) {
  // MapInstance context는 필수입니다 (전역 스토어는 더 이상 지원하지 않음)
  if (!mapInstanceContext?.mapStore) {
    throw new Error(
      "MapInstance context is required. Global map store is no longer supported.",
    );
  }
  const mapStore = mapInstanceContext.mapStore.getState();
  const eventStore = mapInstanceContext.eventStore.getState();
  const layerStore = mapInstanceContext.layerStore.getState();

  try {
    // 로딩 시작
    mapStore.setLoading(true);
    mapStore.setError(null);

    // Core 패키지를 통한 지도 생성
    const { map, odf } = await MapFactory.createMap(container, options);

    // 지도 인스턴스 설정
    mapStore.setMap(map);
    mapStore.setOdf(odf);
    window.odfMap = map;

    // Core 인스턴스들 생성 (한 번만 생성하여 store에 저장)
    const { eventInstance, mapInstance, layerFactory, errors } =
      CoreInstanceManager.createMapCores(map, odf);

    // Store에 저장 - Event는 event-store에, LayerFactory는 layer-store에 저장
    if (eventInstance) eventStore.setEventInstance(eventInstance);
    if (mapInstance) mapStore.setMapInstance(mapInstance);
    if (layerFactory) layerStore.setLayerFactory(layerFactory);

    // 에러가 있으면 로그 출력
    if (errors.length > 0) {
      console.warn("Core 인스턴스 생성 중 일부 오류 발생:", errors);
    }

    // 초기 상태 설정
    await updateMapStateAction(mapInstanceContext);

    // 이벤트 리스너 등록
    setupMapEventsAction(mapInstanceContext);

    // TODO: 지도가 완전히 로드된 시점으로 변경해야함. 예를들어 이벤트리스너로 basemap layer load 가 완료된 시점이라던가..
    // 어떤 기준을 잡고 콜백을 통해 로딩 완료를 알림. 현재는 일정 시간 후에 로딩 완료로 간주로 변경필요.
    setTimeout(async () => {
      mapStore.setLoading(false);

      // 🎯 Map 초기화 완료 후 Controls 초기화
      await initializeBasicControlsForInstance(mapInstanceContext);
    }, 100);
  } catch (error) {
    console.error("지도 초기화 실패:", error);
    mapStore.setLoading(false);
    mapStore.setError(
      error instanceof Error ? error.message : "지도 초기화 실패",
    );
    throw error;
  }
}

/**
 * 지도 상태 업데이트 액션
 * MapController를 통해 현재 상태를 읽어와 store에 반영합니다.
 */
export async function updateMapStateAction(
  mapInstanceContext: MapStoreContextType,
) {
  if (!mapInstanceContext?.mapStore) {
    throw new Error(
      "MapInstance context is required. Global map store is no longer supported.",
    );
  }
  const store = mapInstanceContext.mapStore.getState();
  const mapInstance = store.mapInstance;

  try {
    if (mapInstance) {
      const center = mapInstance.getCenter();
      const zoom = mapInstance.getZoom();

      // 내부 업데이트 함수 사용 (Core 조작 없이 상태만 업데이트)
      store.updateCenter(center);
      store.updateZoom(zoom);

      // 축척 계산
      const scale = `1:${Math.round(591657527.591555 / Math.pow(2, zoom))}`;
      store.setScale(scale);
    }
  } catch (error) {
    console.warn("지도 상태 업데이트 실패:", error);
  }
}

/**
 * 🎯 지도 이벤트 리스너 설정 액션 (새로운 이벤트 시스템 사용)
 *
 * 통합 이벤트 관리 시스템을 사용하여 지도 기본 이벤트들을 등록합니다.
 * - 지도 이동/줌 변경 감지
 * - 마우스 좌표 추적
 */
export function setupMapEventsAction(mapInstanceContext: MapStoreContextType) {
  if (!mapInstanceContext?.mapStore) {
    throw new Error(
      "MapInstance context is required. Global map store is no longer supported.",
    );
  }
  const mapStore = mapInstanceContext.mapStore.getState();
  // eventInstance는 event-store에 저장됨
  const map = mapStore.map;

  if (!map) {
    console.error("map이 없습니다.", {
      map: !!map,
    });
    return;
  }

  // EventStore 가져오기
  const eventStore = mapInstanceContext.eventStore.getState();
  // eventInstance는 event-store에 저장되므로 map-store 의존 제거 (호출부는 내부 참조)

  try {
    // 🗺️ 지도 이동/줌 이벤트 등록
    const mapMoveResult = eventStore.registerEventListener(
      map,
      "moveend",
      () => {
        console.log("🗺️ Map moveend event triggered");
        updateMapStateAction(mapInstanceContext);
      },
      { listenerId: "map-moveend-core" },
    );

    // 마우스 좌표 이벤트는 성능상 제거됨 - 필요시 개별 컴포넌트에서 직접 등록

    console.log("✅ 지도 기본 이벤트 등록 완료:", {
      moveend: mapMoveResult.listenerId,
    });

    // 정리 함수 반환 (필요시 사용)
    return () => {
      mapMoveResult.cleanup();
      console.log("🧹 지도 기본 이벤트 정리 완료");
    };
  } catch (error) {
    console.error("지도 이벤트 리스너 설정 실패:", error);
    return () => {}; // 빈 정리 함수 반환
  }
}

/**
 * 지도 정리 액션
 * 지도 인스턴스와 이벤트 리스너를 정리합니다.
 */
export function cleanupMapAction(mapInstanceContext: MapStoreContextType) {
  if (!mapInstanceContext?.mapStore) {
    throw new Error(
      "MapInstance context is required. Global map store is no longer supported.",
    );
  }
  const store = mapInstanceContext.mapStore.getState();

  // TODO: 이벤트 리스너 정리 구현 필요

  // Store 초기화
  store.reset();
}
