"use client";
import { useEvent, useMap } from "@geon-map/react-odf";
import { Input } from "@geon-ui/react/primitives/input";
import { useEffect, useState } from "react";

export const ScaleWidget = ({ className }: { className?: string }) => {
  const [currentScale, setCurrentScale] = useState<string>("");
  const { registerListener } = useEvent();
  const { map, isReady } = useMap();

  if (!isReady || !map) {
    throw new Error(
      "Map instance has not been initialized. Make sure you're using this hook inside a Map component.",
    );
  }

  useEffect(() => {
    if (!map) return;

    // 초기 축척 값 설정
    const initialResolution = map.getView().getResolution();
    if (initialResolution) {
      const scale = (initialResolution * 100) / 1000;
      const scaleText = `${scale.toFixed(2)}km`;
      setCurrentScale(scaleText);
    }

    // 축척 변경 이벤트 등록
    const cleanup = registerListener(
      map.getView(),
      "change:resolution",
      () => {
        try {
          const resolution = map.getView().getResolution();
          if (resolution) {
            const scale = (resolution * 100) / 1000;
            const scaleText = `${scale.toFixed(2)}km`;
            setCurrentScale(scaleText);
          }
        } catch (e) {
          console.warn("축척 업데이트 실패:", e);
        }
      }
    );

    return cleanup;
  }, [map, registerListener]);

  return (
    <div
      className={`absolute bottom-4 right-50 z-50 bg-white border rounded-md shadow-md px-3 py-2 text-sm ${className}`}
    >
      <Input
        readOnly
        value={currentScale}
        className="w-28 text-center bg-gray-50"
      />
    </div>
  );
};
