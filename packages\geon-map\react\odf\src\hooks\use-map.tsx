import { useCallback, useEffect, useRef } from "react";

import { useStores } from "../contexts/map-store-context";
import { mergeMapOptions, useMapConfig } from "../providers/map-provider";
import { cleanupMapAction, initializeMap } from "../stores/map-actions";
import type { MapInitializeOptions } from "../types/map-types";

interface UseMapOptions extends Partial<MapInitializeOptions> {
  onMapInit?: (mapState: any) => void;
  containerRef?: React.RefObject<HTMLDivElement | null>;
}

/**
 * 통합 지도 컴포넌트
 *
 * @example
 * ```tsx
 * // Map 컴포넌트에서 사용 (지도 초기화)
 * function Map({ children }) {
 *   const containerRef = useRef<HTMLDivElement>(null);
 *   const { isReady, isLoading } = useMap({ containerRef });
 *
 *   return (
 *     <div ref={containerRef}>
 *       {isReady && children}
 *     </div>
 *   );
 * }
 *
 * // 일반 컴포넌트에서 사용 (상태만 읽기)
 * function MapInfo() {
 *   const { center, zoom, isLoading } = useMap();
 *
 *   return (
 *     <div>
 *       <p>중심점: {center.join(', ')}</p>
 *       <p>줌: {zoom}</p>
 *     </div>
 *   );
 * }
 * ```
 */
export function useMap(options: UseMapOptions = {}) {
  // Map 컴포넌트 provider (자체 store 사용)
  // control 별 provider 옵션 받아서 initializeMap 에서 처리

  const initializeRef = useRef(false);

  // containerRef는 옵션으로 전달받음
  const containerRef = options.containerRef;

  // 전역 설정과 병합
  const { defaultOptions } = useMapConfig();
  const mergedOptions = mergeMapOptions(options, defaultOptions);

  // 🎯 독립적인 Map 인스턴스 Context 사용 (전역 스토어는 더 이상 지원하지 않음)
  const storeContext = useStores();
  const { mapStore } = storeContext;

  if (!mapStore) {
    throw new Error(
      "Map component must be used within a MapStoreProvider context.",
    );
  }

  // Store 상태 구독 (독립적인 MapInstance 기반)
  const map = mapStore((state: any) => state.map);
  const odf = mapStore((state: any) => state.odf);
  const isLoading = mapStore((state: any) => state.isLoading);
  const error = mapStore((state: any) => state.error);
  const center = mapStore((state: any) => state.center);
  const zoom = mapStore((state: any) => state.zoom);
  const scale = mapStore((state: any) => state.scale);

  // 베이스맵 상태
  const currentBasemap = mapStore((state: any) => state.currentBasemap);
  const availableBasemaps = mapStore((state: any) => state.availableBasemaps);

  // 지도 초기화 (containerRef가 있을 때만)
  const initialize = useCallback(async () => {
    if (!containerRef?.current || initializeRef.current) return;

    initializeRef.current = true;

    try {
      await initializeMap(containerRef.current, mergedOptions, storeContext);

      // onMapInit 콜백 호출
      options.onMapInit?.({
        error,
      });
    } catch (error) {
      console.error("지도 초기화 실패:", error);
      initializeRef.current = false;
    }
  }, [defaultOptions, options.onMapInit]);

  // 컴포넌트 마운트 시 지도 초기화 (containerRef가 있을 때만)
  useEffect(() => {
    if (containerRef?.current) {
      initialize();
    }
  }, [initialize]);

  // 컴포넌트 언마운트 시 정리
  useEffect(() => {
    return () => {
      if (containerRef?.current) {
        if (storeContext) {
          cleanupMapAction(storeContext);
        }
        initializeRef.current = false;
      }
    };
  }, []);

  // 지도 준비 상태 계산
  const isReady = Boolean(map && odf && !isLoading && !error);

  return {
    // 상태 (모든 컴포넌트용)
    isReady,
    isLoading,
    error,
    center,
    zoom,
    scale,

    // Map 인스턴스 (고급 사용자용)
    map,
    odf,

    // 베이스맵 상태
    currentBasemap,
    availableBasemaps,
  };
}
