// use-scale.ts
import { useEffect, useState } from "react";

import { useStores } from "../contexts/map-store-context";
import { useMap } from "./use-map";

/**
 * 축척 값을 반환하는 커스텀 훅
 */
export const useScale = () => {
  const { map, isReady } = useMap();
  const { mapStore, eventStore } = useStores();

  if (!isReady || !map) {
    throw new Error(
      "Map instance has not been initialized. Make sure you're using this hook inside a Map component.",
    );
  }

  const scaleInstance = mapStore((s: any) => s.scaleInstance);
  const eventInstance = eventStore((s: any) => s.eventInstance);
  const [currentScale, setCurrentScale] = useState<string>("");

  useEffect(() => {
    if (map) {
      const initialScale = map.getView().getResolution() * 100;
      const scale = initialScale / 1000;
      const unit = "km";
      const scaleText = `${scale.toFixed(2)}${unit}`;
      setCurrentScale(scaleText);
    }
  }, [map]);

  useEffect(() => {
    scaleInstance.setMap();
    const eventId = eventInstance.addListener(
      map.getView(),
      "change:resolution",
      () => {
        setCurrentScale(scaleInstance.getScaleValue());
      },
    );
    return () => {
      if (eventId) eventInstance.removeListener(eventId);
    };
  }, [scaleInstance, eventInstance, map]);

  return currentScale;
};
