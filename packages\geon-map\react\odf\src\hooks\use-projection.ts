// hooks/use-projection.ts
import { createMapProjection, Point } from "@geon-map/core";
import { useMemo } from "react";

import { useMap } from "./use-map";

/**
 * 지도 좌표 변환 기능을 제공하는 훅
 *
 * @example
 * ```tsx
 * function MyComponent() {
 *   const { projectGeom, unprojectPoint } = useProjection();
 *
 *   const handleCoordTransform = () => {
 *     const wgs84Point = unprojectPoint([200000, 400000], "EPSG:4326");
 *     console.log("WGS84 좌표:", wgs84Point);
 *   };
 *
 *   return <button onClick={handleCoordTransform}>좌표 변환</button>;
 * }
 * ```
 */
export function useProjection() {
  const { map, isReady } = useMap();

  if (!isReady || !map) {
    throw new Error(
      "Map instance has not been initialized. Make sure you're using this hook inside a Map component.",
    );
  }

  // MapProjection 인스턴스 생성 (메모이제이션)
  const projection = useMemo(() => {
    return createMapProjection(map);
  }, [map]);

  // 좌표 변환 메서드들을 래핑
  return useMemo(() => {
    if (!projection) {
      return {
        projectGeom: () => null,
        unprojectPoint: () => [0, 0] as Point,
        unprojectPoints: () => [] as Point[],
        isReady: false,
      };
    }

    return {
      /**
       * 지오메트리 투영 (입력 좌표계 -> 지도 좌표계)
       */
      projectGeom: (feature: any, epsgCode: string) => {
        return projection.projectGeom(feature, epsgCode);
      },

      /**
       * 포인트 좌표계 변환 (지도 좌표계 -> 입력 좌표계)
       */
      unprojectPoint: (point: Point, epsgCode: string): Point => {
        return projection.unprojectPoint(point, epsgCode);
      },

      /**
       * 여러 포인트 일괄 변환
       */
      unprojectPoints: (points: Point[], epsgCode: string): Point[] => {
        return points.map((point) =>
          projection.unprojectPoint(point, epsgCode),
        );
      },

      /**
       * 준비 상태 확인
       */
      isReady: true,
    };
  }, [projection]);
}
